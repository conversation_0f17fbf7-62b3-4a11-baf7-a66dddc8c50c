
dependencies {

    implementation("org.ow2.asm:asm:${asmVersion}")
    implementation("org.ow2.asm:asm-tree:${asmVersion}")

    testImplementation("org.junit.jupiter:junit-jupiter:${junitVersion}")
    testImplementation("org.junit.jupiter:junit-jupiter-engine:${junitVersion}")
    testImplementation("org.junit.platform:junit-platform-launcher:1.10.1")
    testImplementation("org.assertj:assertj-core:${assertjVersion}")
    testImplementation("org.mockito:mockito-core:${mockitoVersion}")
    testImplementation("com.fasterxml.jackson.core:jackson-databind:${jacksonVersion}")
}

test {
    useJUnitPlatform()
}
