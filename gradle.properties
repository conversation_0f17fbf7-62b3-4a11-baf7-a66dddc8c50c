group=io.github.danielliu1123
version=0.0.1

# dependencies
# https://central.sonatype.com/artifact/org.projectlombok/lombok
lombokVersion=1.18.38
# https://central.sonatype.com/artifact/org.ow2.asm/asm
asmVersion=9.8
# https://central.sonatype.com/artifact/com.fasterxml.jackson.core/jackson-databind
jacksonVersion=2.20.0
# https://central.sonatype.com/artifact/info.picocli/picocli
picocliVersion=4.7.7
# https://github.com/graalvm/native-build-tools
graalvmBuildToolsVersion=0.11.0
# https://github.com/GoodforGod/graalvm-hint
graalvmHintProcessor=1.2.0

# Code quality
# https://plugins.gradle.org/plugin/com.diffplug.gradle.spotless
spotlessVersion=7.2.1
# https://plugins.gradle.org/plugin/com.github.spotbugs
spotbugsVersion=6.2.6
# https://github.com/spring-gradle-plugins/nullability-plugin
nullabilityVersion=0.0.4
# https://github.com/jspecify/jspecify
jspecifyVersion=1.0.0

# Test dependencies
# https://central.sonatype.com/artifact/org.junit.jupiter/junit-jupiter
junitVersion=5.10.1
# https://central.sonatype.com/artifact/org.assertj/assertj-core
assertjVersion=3.24.2
# https://central.sonatype.com/artifact/org.mockito/mockito-core
mockitoVersion=5.8.0

org.gradle.parallel=true
org.gradle.caching=true
# https://docs.gradle.org/9.0.0/userguide/configuration_cache_enabling.html#config_cache:usage:enable
org.gradle.configuration-cache=true
