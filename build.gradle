plugins {
    id "com.diffplug.spotless" version "${spotlessVersion}" apply false
    id "com.github.spotbugs" version "${spotbugsVersion}" apply false
    id "org.graalvm.buildtools.native" version "${graalvmBuildToolsVersion}" apply false
    id "io.spring.nullability" version "${nullabilityVersion}" apply false
}

allprojects {

    apply plugin: "java"
    apply plugin: "java-library"

    java {
        toolchain {
            languageVersion = JavaLanguageVersion.of(21)
        }
    }

    repositories {
        mavenCentral()
    }

    dependencies {
        // add jspecify
        implementation("org.jspecify:jspecify:${jspecifyVersion}")
        compileOnly("org.projectlombok:lombok:${lombokVersion}")
        annotationProcessor("org.projectlombok:lombok:${lombokVersion}")
        testCompileOnly("org.projectlombok:lombok:${lombokVersion}")
        testAnnotationProcessor("org.projectlombok:lombok:${lombokVersion}")

        testImplementation("org.springframework.boot:spring-boot-starter-test:3.5.5") {
            exclude(group: "org.springframework")
            exclude(group: "org.springframework.boot")
        }
    }

    test {
        useJUnitPlatform()
    }

    apply plugin: "com.diffplug.spotless"
    spotless {
        encoding "UTF-8"
        java {
            toggleOffOn()
            removeUnusedImports()
            trimTrailingWhitespace()
            endWithNewline()
            palantirJavaFormat()

            targetExclude("build/generated/**")

            custom("Refuse wildcard imports", {
                if (it =~ /\nimport .*\*;/) {
                    throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
                }
            } as Closure<String>)
        }
    }

    apply plugin: "com.github.spotbugs"
    spotbugs {
        spotbugsTest.enabled = false
        omitVisitors.addAll("FindReturnRef", "DontReusePublicIdentifiers")
    }

    apply plugin: "io.spring.nullability"
}
