# Jarinker Quick Start

```bash
curl -o examples/quick-start/libs/guava-33.4.8-jre.jar https://repo1.maven.org/maven2/com/google/guava/guava/33.4.8/guava-33.4.8-jre.jar
# Using Jarinker CLI from source code (specify JAR file directly)
./gradlew :jarinker-cli:run --args="analyze -s $(pwd)/examples/quick-start/build/classes/java/main -d $(pwd)/examples/quick-start/libs/guava-32.1.3-jre.jar --verbose"
./gradlew :jarinker-cli:run --args="shrink -s $(pwd)/examples/quick-start/build/classes/java/main -d $(pwd)/examples/quick-start/libs/guava-32.1.3-jre.jar -o $(pwd)/examples/quick-start/shrunk-libs --verbose"
du -sh examples/quick-start/libs/guava-32.1.3-jre.jar
du -sh examples/quick-start/shrunk-libs/guava-32.1.3-jre-shrunk.jar 
```
