package jarinker.cli;

import static io.goodforgod.graalvm.hint.annotation.ReflectionHint.AccessType.ALL_DECLARED;
import static io.goodforgod.graalvm.hint.annotation.ReflectionHint.AccessType.ALL_PUBLIC;

import io.goodforgod.graalvm.hint.annotation.ReflectionHint;
import jarinker.cli.cmd.RootCommand;
import jarinker.cli.cmd.analyze.AnalyzeCommand;
import jarinker.cli.cmd.shrink.ShrinkCommand;
import picocli.AutoComplete;
import picocli.CommandLine;

/**
 * <AUTHOR>
 */
@ReflectionHint(
        types = AutoComplete.GenerateCompletion.class,
        value = {ALL_DECLARED, ALL_PUBLIC
        }) // for GenerateCompletion, picocli only generate graalvm reflection config for "your own" commands
public class Main {

    public static void main(String[] args) {

        var rootCmd = new CommandLine(new RootCommand())
                .addSubcommand("completion", new AutoComplete.GenerateCompletion())
                .addSubcommand(new AnalyzeCommand())
                .addSubcommand(new ShrinkCommand());

        System.exit(rootCmd.execute(args));
    }
}

// use curl to download guava to specified directory
// curl -o ./examples/quick-start/libs/guava-33.4.8-jre.jar https://repo1.maven.org/maven2/com/google/guava/guava/33.4.8/guava-33.4.8-jre.jar
